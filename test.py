from maix import image, camera, display, app, uart, pinmap

import cv2

import numpy as np


# 初始化串口
pinmap.set_pin_function("A17", "UART0_RX")
pinmap.set_pin_function("A16", "UART0_TX")


device = "/dev/ttyS0"

serial = uart.UART(device, 9600)

# 初始化摄像头和显示器

cam = camera.Camera(320, 240, fps=80)

disp = display.Display()



# 预计算形态学核，避免重复创建
kernel_rect = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

MIN_AREA = 1000  # 降低最小面积阈值，适应侧对时的较小投影面积
MAX_AREA = 80000

# 简化矫正参数
ENABLE_SIMPLE_CORRECTION = True    # 是否启用简化矫正
SHOW_CORRECTION_DEBUG = True       # 是否显示矫正对比点
CORRECTION_THRESHOLD = 1.3         # 变形检测阈值

# 移除了不再使用的函数：sort_rect_points, match_corners_by_distance, is_similar_rect
# 这些函数在优化后的算法中不再需要

def simple_perspective_correction(rect_points):
    """
    简化版透视矫正 - 基于几何特征的中心点补偿
    计算量小，适合实时应用
    """
    pts = np.array(rect_points, dtype=np.float32)

    # 计算四条边的长度
    side_lengths = []
    side_vectors = []
    for i in range(4):
        p1 = pts[i]
        p2 = pts[(i+1) % 4]
        vector = p2 - p1
        length = np.linalg.norm(vector)
        side_lengths.append(length)
        side_vectors.append(vector)

    # 计算基础几何中心
    geometric_center = np.mean(pts, axis=0)

    # 检测矩形的变形程度
    max_side = max(side_lengths)
    min_side = min(side_lengths)
    aspect_ratio = max_side / min_side if min_side > 0 else 1.0

    # 只在明显变形时进行补偿（减少不必要的计算）
    if aspect_ratio < CORRECTION_THRESHOLD:  # 变形不明显，直接返回几何中心
        return tuple(map(int, geometric_center))

    # 计算矩形的主方向（最长边的方向）
    max_side_idx = side_lengths.index(max_side)
    main_direction = side_vectors[max_side_idx]
    main_direction = main_direction / np.linalg.norm(main_direction)  # 归一化

    # 计算矩形相对于水平方向的倾斜角度
    angle = np.arctan2(main_direction[1], main_direction[0])
    angle_deg = abs(np.degrees(angle))

    # 根据倾斜角度和长宽比计算补偿量
    if angle_deg > 15:  # 只在倾斜角度较大时补偿
        # 计算补偿向量（垂直于主方向）
        perpendicular = np.array([-main_direction[1], main_direction[0]])

        # 补偿强度基于变形程度和角度
        compensation_strength = min(10, (aspect_ratio - 1) * 5 * (angle_deg / 45))

        # 确定补偿方向（向矩形的"真实"中心偏移）
        # 通过分析点的分布来判断补偿方向
        center_to_points = pts - geometric_center
        projection_on_perp = np.dot(center_to_points, perpendicular)
        avg_projection = np.mean(projection_on_perp)

        # 补偿向量
        compensation = perpendicular * compensation_strength * np.sign(avg_projection)
        corrected_center = geometric_center - compensation
    else:
        corrected_center = geometric_center

    return tuple(map(int, corrected_center))

def is_rectangle(approx):
    if approx is None or len(approx) != 4 or not cv2.isContourConvex(approx):
        return False

    pts = np.array([point[0] for point in approx], dtype=np.float32)

    # 向量化计算所有角度
    def calculate_angles(points):
        # 计算所有向量
        v1 = np.roll(points, -1, axis=0) - points  # 当前点到下一点
        v2 = np.roll(points, 1, axis=0) - points   # 当前点到上一点

        # 向量长度
        norm1 = np.linalg.norm(v1, axis=1)
        norm2 = np.linalg.norm(v2, axis=1)

        # 避免除零
        valid = (norm1 > 0) & (norm2 > 0)
        angles = np.zeros(4)

        if np.any(valid):
            # 计算夹角
            cos_angles = np.sum(v1[valid] * v2[valid], axis=1) / (norm1[valid] * norm2[valid])
            cos_angles = np.clip(cos_angles, -1.0, 1.0)
            angles[valid] = np.arccos(cos_angles) * 180 / np.pi

        return angles

    angles = calculate_angles(pts)
    return np.all((angles > 60) & (angles < 120))  # 向量化比较

# 主循环

while not app.need_exit():

    try:
 

        img = cam.read()



        if img is None:

            continue
 
 
 
        try:
 
            img_raw = image.image2cv(img, copy=True)
 
        except Exception as e:
 
            print("图像转换失败:", e)
 
            continue
 
 
 
        rect_center = (-1, -1)  # 替换midpoints变量
 
 
 
        # 矩形检测与中点计算

        try:

            gray = cv2.cvtColor(img_raw, cv2.COLOR_BGR2GRAY)

            bin_img = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C,

                                            cv2.THRESH_BINARY, 11, 2)

            closed = cv2.morphologyEx(bin_img, cv2.MORPH_CLOSE, kernel_rect)

            contours, _ = cv2.findContours(closed, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

            # 按面积排序轮廓，优先处理较小的（目标是最小矩形）
            contours_with_area = [(cv2.contourArea(c), c) for c in contours]
            contours_with_area.sort(key=lambda x: x[0])  # 按面积从小到大排序

            img_h, img_w = img_raw.shape[:2]
            margin = 1

            # 直接在检测过程中维护最小矩形
            min_rect = None
            min_area = float('inf')

            for area, contour in contours_with_area:
                # 面积检查
                if area < MIN_AREA:
                    continue
                if area > MAX_AREA:
                    break  # 由于已排序，后面的都会更大

                # 边界检查（使用预计算的图像尺寸）
                x, y, w, h = cv2.boundingRect(contour)
                if x < margin or y < margin or x + w > img_w - margin or y + h > img_h - margin:
                    continue

                # 轮廓近似
                perimeter = cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, 0.03 * perimeter, True)

                if is_rectangle(approx):
                    rect = [tuple(map(int, pt[0])) for pt in approx]
                    rect_area = cv2.contourArea(np.array(rect, dtype=np.int32))

                    if rect_area < min_area:
                        min_area = rect_area
                        min_rect = rect

            # 绘制最小矩形和计算中心点
            if min_rect:
                # 绘制最小矩形轮廓
                cv2.drawContours(img_raw, [np.array(min_rect, dtype=np.int32)], -1, (0, 255, 0), 2)

                # 原始几何中心点
                original_center_x = sum(pt[0] for pt in min_rect) // 4
                original_center_y = sum(pt[1] for pt in min_rect) // 4
                original_center = (original_center_x, original_center_y)

                # 简化透视矫正
                if ENABLE_SIMPLE_CORRECTION:
                    try:
                        corrected_center = simple_perspective_correction(min_rect)
                        rect_center = corrected_center

                        # 显示两个中心点对比（可选，用于调试）
                        if SHOW_CORRECTION_DEBUG:
                            cv2.circle(img_raw, original_center, 3, (0, 0, 255), 1)      # 红色圆圈：原始中心
                            cv2.circle(img_raw, rect_center, 2, (0, 255, 255), -1)      # 青色实心：矫正中心
                        else:
                            cv2.circle(img_raw, rect_center, 2, (0, 255, 255), -1)      # 青色实心：矫正中心

                    except Exception as e:
                        print(f"简化矫正失败: {e}")
                        rect_center = original_center
                        cv2.circle(img_raw, rect_center, 2, (0, 255, 255), -1)
                else:
                    rect_center = original_center
                    cv2.circle(img_raw, rect_center, 2, (0, 255, 255), -1)  # 青色小实心圆
            else:
                rect_center = (-1, -1)

        except Exception as e:

            print("矩形检测异常:", e)
 
 
 
        # 发送矩形中心点坐标

        try:
            if rect_center[0] >= 0 and rect_center[1] >= 0:
                message = f":{rect_center[0]},{rect_center[1]},0#"
                print(f"矩形中心点: ({rect_center[0]}, {rect_center[1]})")
            else:
                message = f":0,0,0#"
                print("矩形中心点: (0, 0)")

            serial.write(message.encode('utf-8'))

        except Exception as e:

            print("串口发送异常:", e)
 
 
 
        # 显示图像

        try:
            img_show = image.cv2image(img_raw, copy=False)

            disp.show(img_show)

        except Exception as e:

            print("图像显示失败:", e)
 
 
    except Exception as e:
 
        print("主循环异常:", e)
